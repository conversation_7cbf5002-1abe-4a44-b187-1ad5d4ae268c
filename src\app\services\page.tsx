"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { ChevronRight, Sparkles } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { BeamsBackground } from "@/components/ui/beams-background";
import { Clock } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ServiceDetails } from "@/components/ui/service-details";
import { SiteFooter } from "@/components/ui/site-footer";
import { ServiceDialog } from "@/components/ui/service-dialog";
// import { fetchReviewsFromUrls, GoogleReview } from "@/lib/fetchGoogleReviews";
// import { reviewsConfig, isGoogleReviewsConfigured } from "@/config/reviews";

// Type declarations are now handled in script-loader.tsx

export default function ServicesPage() {
  const [isMounted, setIsMounted] = React.useState(false);
  const [openServiceId, setOpenServiceId] = React.useState<string | null>(null);
  const [scriptsLoaded, setScriptsLoaded] = React.useState(false);
  // const [testimonials, setTestimonials] = React.useState<GoogleReview[]>([]);
  // const [isLoadingReviews, setIsLoadingReviews] = React.useState(true);
  // const [reviewsSource, setReviewsSource] = React.useState<'google' | 'local' | 'error'>('local');
  // const [reviewsMessage, setReviewsMessage] = React.useState<string>('');

  React.useEffect(() => {
    setIsMounted(true);

    // Handle scroll to reviews section if hash is present or sessionStorage indicates scroll target
    const handleScrollToReviews = () => {
      const scrollTarget = sessionStorage.getItem('scrollToElement');
      const hasHashTarget = typeof window !== 'undefined' && window.location.hash === '#google-reviews';

      if (hasHashTarget || scrollTarget === 'google-reviews') {
        // Clear the sessionStorage item immediately
        if (scrollTarget) {
          sessionStorage.removeItem('scrollToElement');
        }

        // Small delay to ensure content is rendered, then scroll
        setTimeout(() => {
          const documentHeight = document.documentElement.scrollHeight;

          // Scroll to about 85% down the page to reach the reviews section
          const scrollToPosition = documentHeight * 0.85;

          window.scrollTo({
            top: scrollToPosition,
            behavior: 'smooth'
          });

          console.log(`Document height: ${documentHeight}px, scrolling to: ${scrollToPosition}px`);
        }, 100); // Very short delay just to ensure content is rendered
      }
    };

    handleScrollToReviews();

    // Set scripts loaded to true after a short delay to allow ScriptLoader to handle widget loading
    const timeout = setTimeout(() => {
      setScriptsLoaded(true);
    }, 1000);

    return () => {
      clearTimeout(timeout);
    };
  }, []);



  // Featured services data
  const featuredServices = [
    {
      id: "facial-1",
      name: "Customized Facials",
      description: "Your glow isn&apos;t one-size fits all and neither are our facials.",
      image: "/images/services/facials/customized-facial-tile.jpg",
      price: 99,
      duration: "60 min",
      featured: true,
      link: "#facial-details",
      badge: {
        text: "Most Popular",
        variant: "default" as const
      }
    },
    {
      id: "massage-1",
      name: "Lash Lift",
      description: "Enhance your natural lashes with our signature lift treatment.",
      image: "/images/lash-lift-service.jpg",
      price: 65,
      duration: "60 min",
      link: "#massage-details",
      badge: {
        text: "Staff Pick",
        variant: "secondary" as const
      }
    },
    {
      id: "facial-2",
      name: "Collagen Induction",
      description: "Step into your glow. Glass skin unlocked.",
      image: "/images/microneedling-service.jpg",
      price: 135,
      duration: "90 min",
      link: "#collagen-therapy-details",
      badge: {
        text: "Premium",
        variant: "outline" as const
      }
    },
    {
      id: "massage-2",
      name: "Waxing Services",
      description: "Wax on, glow up - because smooth skin is a vibe.",
      image: "/images/services/waxing/waxing-1.jpg",
      price: "12-85",
      duration: "15-60 min",
      link: "#waxing-details"
    },
    {
      id: "package-1",
      name: "The Glow Edit",
      description: "Full-glam moment. It&apos;s the all-in-one, because you deserve the works.",
      image: "/images/services/glow-edit/glow-edit-2.jpg",
      price: 145,
      duration: "90 min",
      featured: true,
      link: "#package-details",
      badge: {
        text: "Best Value",
        variant: "default" as const
      }
    },
    {
      id: "facial-3",
      name: "A La Carte",
      description: "Specialty add ons. Brow Tinting, Skin Lightening, Ear Seeding.",
      image: "/images/spa-gallery-1.png",
      price: "17-53",
      duration: "15-30 min",
      link: "#lacarte-details",
      badge: {
        text: "New",
        variant: "secondary" as const
      }
    },
  ];

  // Detailed service information
  const facialDetails = {
    id: "facial-details",
    name: "Personalized Facial Treatments",
    price: 99,
    duration: "60 minutes",
    description: "Unlock your signature glow, one facial at a time.",
    benefits: [
      "DEEP CLEANSING TO CLEAR OUT IMPURITIES AND RESET YOUR SKIN.",
      "EXFOLIATION TO SMOOTH TEXTURE AND REVEAL YOUR NATURAL GLOW.",
      "CUSTOMIZED MASKS MADE FOR YOUR SKIN TYPE.",
      "ADVANCED HYDRATION THERAPY FOR THAT DEWY, PLUMP FINISH.",
      "LYMPHATIC FACIAL MASSAGE TO DEPUFF, SCULPT, AND BOOST CIRCULATION.",
      "FINE LINE + WRINKLE REDUCTION FOR THAT FRESH YOUTHFUL VIBE."
    ],
    details: "This facial combines expert skin analysis, advanced techniques, and premium modalities for fully customized, result-driven experience. Relaxation meets radiance.",
    process: [
      {
        title: "Skin Analysis",
        description: "We begin with a thorough evaluation of your skin to identify specific concerns and customize your treatment."
      },
      {
        title: "Deep Cleansing",
        description: "A gentle cleanser is applied to remove makeup, dirt, and impurities from your skin."
      },
      {
        title: "Exfoliation",
        description: "We use a mild exfoliant to remove dead skin cells and reveal fresher, brighter skin beneath."
      },
      {
        title: "Extraction (if needed)",
        description: "Professional extraction of blackheads and whiteheads is performed if necessary."
      },
      {
        title: "Custom Mask Application",
        description: "A specialized mask is applied based on your skin&apos;s specific needs and concerns."
      },
      {
        title: "Hydration & Protection",
        description: "The treatment concludes with application of serums, moisturizer, and SPF protection."
      }
    ],
    images: [
      "/images/facial-treatment-hero.jpg",
      "/images/services/equipment/jade-mask-and-brush.png",
      "/images/before-after-results.png"
    ],
    popularChoice: true
  };

  const massageDetails = {
    id: "massage-details",
    name: "Lash Lift Treatment",
    price: 65,
    duration: "60 minutes",
    description: "Transform your natural lashes with our signature lift treatment for a wide-eyed, curled effect that lasts for weeks.",
    benefits: [
      "ENHANCES YOUR NATURAL LASHES WITHOUT EXTENSIONS.",
      "CREATES A BEAUTIFUL CURLED EFFECT.",
      "OPENS UP THE EYE AREA FOR A MORE AWAKE LOOK.",
      "RESULTS LAST 6-8 WEEKS.",
      "LOW MAINTENANCE AFTERCARE.",
      "WATERPROOF AND SUITABLE FOR ACTIVE LIFESTYLES."
    ],
    details: "Our lash lift treatment is the perfect solution for those who want to enhance their natural lashes without the maintenance of extensions. The process gently lifts your natural lashes from the root, creating a beautiful curl that makes your eyes appear more open and your lashes look longer. We use premium products that also condition your lashes during the treatment. The results last 6-8 weeks as your lashes naturally grow and shed. This treatment is ideal for those with straight lashes, those who want a low-maintenance beauty routine, or anyone looking for a natural enhancement to their eyes.",
    process: [
      {
        title: "Consultation",
        description: "We discuss your desired look and assess your natural lashes to customize your treatment."
      },
      {
        title: "Preparation",
        description: "Your eye area is gently cleansed and lashes are separated to ensure precision."
      },
      {
        title: "Lift Application",
        description: "A silicone rod is applied to your eyelid and lashes are carefully positioned over it."
      },
      {
        title: "Processing",
        description: "A lifting solution is applied to reshape the lash structure, followed by a setting solution."
      },
      {
        title: "Tinting (Optional)",
        description: "A color tint can be applied to darken the lashes for an even more dramatic effect."
      },
      {
        title: "Nourishment",
        description: "A nourishing keratin treatment is applied to condition and strengthen your lashes."
      }
    ],
    images: [
      "/images/lash-lift-service.jpg",
      "/images/services/lash-lift/lash-lift-1.jpg",
      "/images/services/lash-lift/lash-lift-2.jpg"
    ]
  };

  const packageDetails = {
    id: "package-details",
    name: "The Glow Edit",
    price: 145,
    duration: "90 minutes",
    description: "One-and-Done facial experience. Personalized, Elevated, and glow-loaded from start to finish.",
    benefits: [
      "FULL SPECTRUM GLOW-UP, DEEP CLEANSING, EXFOLIATION, AND HYDRATION FOR RADIANT SKIN.",
      "TARGETED MASKING BASED ON YOUR SKIN&apos;S UNIQUE NEEDS.",
      "FACIAL SCULPTING & LYMPHATIC DRAINAGE TO LIFT, DEPUFF, AND BOOST CIRCULATION.",
      "OPTIONAL BROW, LIP, AND NOSE WAXING INCLUDED FOR A POLISHED FINISH.",
      "BROW TINTING AVAILABLE FOR THAT EXTRA DEFINITION AND POP.",
      "GLOW BOOSTING FINISH WITH PREMIUM SERUMS + TAILORED TECHNIQUES FOR LASTING RADIANCE."
    ],
    details: "The ultimate facial experience. This all-inclusive facial goes beyond basics. Combining expert techniques, thoughtful touches, and luxe upgrades to give a customized experience from start to finish. It&apos;s everything your skin (and brows) could ask for.",
    process: [
      {
        title: "Skin Analysis",
        description: "We begin with a thorough evaluation of your skin to identify specific concerns and customize your treatment."
      },
      {
        title: "Deep Cleansing",
        description: "A gentle cleanser is applied to remove makeup, dirt, and impurities from your skin."
      },
      {
        title: "Exfoliation",
        description: "We use a mild exfoliant to remove dead skin cells and reveal fresher, brighter skin beneath."
      },
      {
        title: "Extraction (if needed)",
        description: "Professional extraction of blackheads and whiteheads is performed if necessary."
      },
      {
        title: "Facial Sculpting & Waxing",
        description: "Facial massage techniques to lift and sculpt, plus optional waxing services for brows, lip, or chin."
      },
      {
        title: "Brow Tinting (Optional)",
        description: "Professional brow tinting for enhanced definition that frames your face perfectly."
      }
    ],
    images: [
      "/images/services/glow-edit/glow-edit-1.jpg",
      "/images/services/glow-edit/glow-edit-2.jpg",
      "/images/services/glow-edit/glow-edit-3.jpg"
    ],
    popularChoice: true
  };

  // Add missing treatment details
  const waxingDetails = {
    id: "waxing-details",
    name: "Waxing Services",
    price: "12-85",
    duration: "15-60 minutes",
    description: "Achieve smooth, glowing skin with our professional waxing services tailored to your specific needs.",
    benefits: [
      "QUICK AND EFFECTIVE HAIR REMOVAL.",
      "SMOOTHER SKIN FOR LONGER PERIODS.",
      "LESS FREQUENT TREATMENTS NEEDED COMPARED TO SHAVING.",
      "GENTLER ON SENSITIVE SKIN WITH OUR PREMIUM PRODUCTS.",
      "PROFESSIONAL TECHNIQUE MINIMIZES DISCOMFORT.",
      "PREVENTS INGROWN HAIRS AND IRRITATION."
    ],
    details: "Our waxing services use premium, gentle formulas that effectively remove unwanted hair while being kind to your skin. We follow strict hygiene protocols and use techniques that minimize discomfort. Whether you&apos;re looking for facial waxing, body waxing, or specialized areas, our skilled estheticians customize the treatment for your skin type and sensitivity level. We provide thorough aftercare instructions to keep your skin smooth and irritation-free between appointments.",
    process: [
      {
        title: "Preparation",
        description: "The area is cleansed and prepped with a gentle antiseptic to ensure hygiene."
      },
      {
        title: "Application",
        description: "Our premium wax is applied at the perfect temperature for comfort and effectiveness."
      },
      {
        title: "Removal",
        description: "Quick, expert removal technique minimizes discomfort while removing hair from the root."
      },
      {
        title: "Post-Treatment Care",
        description: "Soothing and calming products are applied to reduce redness and irritation."
      },
      {
        title: "Aftercare Guidance",
        description: "You&apos;ll receive instructions on how to maintain smooth skin and prevent ingrown hairs."
      }
    ],
    images: [
      "/images/services/waxing/waxing-1.jpg",
      "/images/services/waxing/waxing-2.jpg",
      "/images/services/waxing/waxing-3.jpg"
    ]
  };

  const laCarteDetails = {
    id: "lacarte-details",
    name: "A La Carte Enhancements",
    price: "17-53",
    duration: "15-30 minutes",
    description: "Specialized add-on treatments to customize and enhance your beauty experience with premium techniques.",
    benefits: [
      "CUSTOMIZE YOUR TREATMENT EXPERIENCE.",
      "TARGET SPECIFIC CONCERNS OR AREAS.",
      "ENHANCE YOUR ALREADY GLOWING RESULTS.",
      "EXPRESS SERVICES FOR BUSY SCHEDULES.",
      "PROFESSIONAL-GRADE TREATMENTS.",
      "PERFECT FOR SPECIAL OCCASIONS."
    ],
    details: "Our A La Carte menu offers specialized enhancements that can be added to any service or enjoyed as standalone treatments. From brow tinting that frames your face perfectly to skin lightening that addresses hyperpigmentation, each service is designed to elevate your beauty routine. These targeted treatments use professional-grade products and techniques to deliver noticeable results in a short time. They&apos;re perfect for maintaining your glow between full treatments or preparing for special events.",
    process: [
      {
        title: "Consultation",
        description: "We discuss your specific concerns and desired outcome to customize your enhancement."
      },
      {
        title: "Preparation",
        description: "The area is cleansed and prepped for optimal product absorption and results."
      },
      {
        title: "Application",
        description: "Specialized techniques and premium products are used for your specific enhancement."
      },
      {
        title: "Processing",
        description: "Products are allowed to properly develop when needed (tinting, masks, etc.)."
      },
      {
        title: "Finishing Touches",
        description: "Final adjustments ensure your enhancement looks natural and beautiful."
      }
    ],
    images: [
      "/images/services/lacarte/lacarte-1.jpg",
      "/images/spa-gallery-1.png",
      "/images/services/general/vie-healing-product.png"
    ],
    badge: {
      text: "New",
      variant: "secondary" as const
    }
  };

  const collagenTherapyDetails = {
    id: "collagen-therapy-details",
    name: "Collagen Induction",
    price: 135,
    duration: "90 minutes",
    description: "Step into your glow. Glass skin unlocked.",
    benefits: [
      "STIMULATES NATURAL COLLAGEN PRODUCTION.",
      "REDUCES FINE LINES AND WRINKLES.",
      "IMPROVES SKIN TEXTURE AND ELASTICITY.",
      "MINIMIZES APPEARANCE OF SCARS AND HYPERPIGMENTATION.",
      "NON-INVASIVE TREATMENT WITH MINIMAL DOWNTIME.",
      "LONG-LASTING RESULTS WITH PROPER MAINTENANCE."
    ],
    details: "Our Collagen Therapy Treatment is an advanced, non-invasive procedure designed to rejuvenate your skin from the inside out. By stimulating your skin&apos;s natural collagen production, this treatment effectively reduces the appearance of fine lines, wrinkles, and scars while improving overall texture and elasticity. The procedure creates micro-channels in the skin, which triggers the body&apos;s natural healing process, resulting in increased collagen and elastin production. This cutting-edge treatment is suitable for various skin concerns, including aging skin, sun damage, acne scarring, and hyperpigmentation.",
    process: [
      {
        title: "Skin Analysis & Consultation",
        description: "We begin with a thorough evaluation of your skin condition and discuss your specific concerns and goals."
      },
      {
        title: "Preparation",
        description: "Your skin is cleansed and a numbing cream is applied to ensure your comfort during the procedure."
      },
      {
        title: "Micro-Needling Procedure",
        description: "Using a specialized device, we create controlled micro-injuries to stimulate collagen production."
      },
      {
        title: "Serum Application",
        description: "Premium collagen-boosting serums are applied, which can penetrate deeply through the micro-channels."
      },
      {
        title: "Collagen Mask",
        description: "A soothing collagen mask is applied to calm the skin and enhance the treatment benefits."
      },
      {
        title: "Post-Treatment Care",
        description: "You&apos;ll receive detailed aftercare instructions to maximize results and maintain your refreshed appearance."
      }
    ],
    images: [
      "/images/services/microneedling/collagen-induction-1.jpg",
      "/images/services/microneedling/collagen-induction-2.jpg"
    ],
    badge: {
      text: "Premium",
      variant: "outline" as const
    }
  };

  if (!isMounted) {
    return null;
  }

  const handleServiceClick = (serviceId: string) => {
    setOpenServiceId(serviceId);
  };

  return (
    <div className="relative min-h-screen bg-background">
      <BeamsBackground />

      {/* Services Section */}
      <section className="relative">
        {/* Services Hero Section - Beautiful Full Redesign with Perfect Mobile Fit */}
        <section className="relative min-h-[100vh] sm:min-h-[90vh] md:min-h-[75vh] flex items-center overflow-hidden">
          {/* Background image with overlay - optimized for mobile */}
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/facial-treatment-hero.jpg"
              alt="Spa services background"
              fill
              priority
              sizes="100vw"
              className="object-cover object-center"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-stone-900/80 via-stone-900/60 to-stone-900/80" />
          </div>

          {/* Decorative elements - adjusted for mobile */}
          <div className="absolute top-1/4 left-0 sm:left-10 w-64 sm:w-96 h-64 sm:h-96 rounded-full bg-[#b07c70]/10 blur-3xl animate-float"></div>
          <div className="absolute bottom-1/4 right-0 sm:right-10 w-64 sm:w-96 h-64 sm:h-96 rounded-full bg-[#8b5d53]/10 blur-3xl animate-float-delay"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-transparent via-stone-900/5 to-transparent opacity-70"></div>

          {/* Content container - improved for mobile */}
          <div className="container relative z-10 mx-auto px-4 py-12 sm:py-0">
            <div className="flex flex-col items-center text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="relative inline-flex items-center gap-2 px-4 sm:px-6 py-2 sm:py-3 backdrop-blur-sm rounded-full border border-[#d4b2a7]/40 mb-4 sm:mb-6 shadow-lg overflow-hidden group transition-all duration-300 hover:shadow-xl">
                  {/* Elegant gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[#5d3f39] via-[#8b5d53] to-[#b07c70] opacity-100 group-hover:opacity-90 transition-opacity duration-300"></div>

                  {/* Subtle shine effect on hover */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 bg-[length:200%_100%] animate-[shine_1.5s_ease-in-out_infinite]"></div>

                  {/* Hashtag symbol with gradient */}
                  <div className="relative flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 rounded-full overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-[#d4b2a7] to-[#b07c70] opacity-90"></div>
                    <span className="relative text-white font-bold text-base sm:text-lg">#</span>
                  </div>

                  {/* Text with enhanced styling */}
                  <h2 className="text-lg sm:text-xl font-bold tracking-wider relative z-10 text-white">
                    GlowGoals
                  </h2>
                </div>
              </motion.div>

              <motion.h1
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-light text-white mb-4 sm:mb-6 leading-tight px-2 tracking-wide"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                READY TO GLOW?
              </motion.h1>

              <motion.p
                className="text-lg sm:text-xl text-white/90 font-serif italic mb-8 sm:mb-10 max-w-xl mx-auto px-4 tracking-wide"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                Uncover the glow experience. Glowing up should feel as good as it looks.
              </motion.p>
            </div>
          </div>

          {/* Bottom decorative elements - improved for mobile */}
          <div className="absolute bottom-0 left-0 w-full h-16 sm:h-24 bg-gradient-to-t from-[#f8f1ee]/90 to-transparent z-10"></div>
          <div className="absolute -bottom-1 sm:-bottom-5 left-0 w-full">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto opacity-90">
              <path fill="#f8f1ee" fillOpacity="1" d="M0,224L60,213.3C120,203,240,181,360,181.3C480,181,600,203,720,208C840,213,960,203,1080,176C1200,149,1320,107,1380,85.3L1440,64L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"></path>
            </svg>
          </div>
        </section>

        {/* Featured Services Grid - Desktop */}
        <section className="hidden md:block py-16 bg-stone-50/30">
          <div className="container mx-auto px-4">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-light tracking-wide mb-3 text-[#5d3f39] uppercase font-playfair">Discover Your Glow Ritual</h2>
              <p className="text-[#5d3f39]/80 max-w-xl mx-auto font-playfair italic text-lg">
                Every ritual is a step closer to your ultimate glow.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-4"></div>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredServices.map((service) => (
                <div
                  key={service.id}
                  className="relative bg-white/90 rounded-lg shadow-sm hover:shadow-md border border-[#8b5d53]/10 overflow-hidden transition-all duration-300 hover:-translate-y-1 flex flex-col"
                >
                  <div className="relative h-48 lg:h-52">
                    <Image
                      src={service.image}
                      alt={service.name}
                      fill
                      className="object-cover"
                    />
                    {service.badge && (
                      <div className="absolute top-3 right-3 z-10">
                        <Badge
                          variant={service.badge.variant}
                          className={cn(
                            "text-sm",
                            service.badge.variant === "default" && "bg-[#8b5d53] text-white",
                            service.badge.variant === "secondary" && "bg-stone-700 text-white",
                            service.badge.variant === "outline" && "bg-white/80 text-[#8b5d53] border-[#8b5d53]"
                          )}
                        >
                          {service.badge.text === "Most Popular" ||
                           service.badge.text === "Best Value" ||
                           service.badge.text === "Featured" ? (
                            <><Sparkles className="w-4 h-4 mr-1" /> {service.badge.text}</>
                          ) : (
                            service.badge.text
                          )}
                        </Badge>
                      </div>
                    )}
                  </div>

                  <div className="p-5 flex flex-col flex-grow">
                    <h3 className="text-xl font-light text-[#5d3f39] mb-2 font-playfair">{service.name}</h3>
                    <p className="text-sm text-[#5d3f39]/80 mb-4 line-clamp-2 font-light font-playfair flex-grow">{service.description}</p>
                    <div className="flex items-center justify-between mt-auto">
                      <div className="flex items-center space-x-3">
                        <Clock className="w-4 h-4 text-[#8b5d53]" />
                        <span className="text-sm text-[#5d3f39]/70">{service.duration}</span>
                        <span className="text-sm text-[#5d3f39] font-medium">
                          ${typeof service.price === 'string' && service.price.includes('-')
                            ? service.price.split('-')[0]
                            : service.price}+
                        </span>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-[#8b5d53] hover:text-[#5d3f39] hover:bg-[#8b5d53]/10 p-2 h-auto"
                        onClick={() => handleServiceClick(
                          service.id === "facial-1" ? "facial-details" :
                          service.id === "massage-1" ? "massage-details" :
                          service.id === "facial-2" ? "collagen-therapy-details" :
                          service.id === "massage-2" ? "waxing-details" :
                          service.id === "facial-3" ? "lacarte-details" :
                          service.id === "package-1" ? "package-details" :
                          service.id
                        )}
                      >
                        View Details <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Mobile Services Grid - Only visible on mobile */}
        <section className="md:hidden py-8 px-4 bg-stone-50/30">
          <div className="container mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-light tracking-wide mb-2 text-[#5d3f39] uppercase font-playfair">Our Services</h2>
              <p className="text-[#5d3f39]/80 max-w-sm mx-auto font-playfair italic text-sm">
                Every ritual is a step closer to your ultimate glow.
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-3"></div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {featuredServices.map((service) => (
                <div
                  key={service.id}
                  className="relative bg-white/90 rounded-lg shadow-sm hover:shadow-md border border-[#8b5d53]/10 overflow-hidden transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="relative h-40">
                    <Image
                      src={service.image}
                      alt={service.name}
                      fill
                      className="object-cover"
                    />
                    {service.badge && (
                      <div className="absolute top-2 right-2 z-10">
                        <Badge
                          variant={service.badge.variant}
                          className={cn(
                            "text-xs",
                            service.badge.variant === "default" && "bg-[#8b5d53] text-white",
                            service.badge.variant === "secondary" && "bg-stone-700 text-white",
                            service.badge.variant === "outline" && "bg-white/80 text-[#8b5d53] border-[#8b5d53]"
                          )}
                        >
                          {service.badge.text === "Most Popular" ||
                           service.badge.text === "Best Value" ||
                           service.badge.text === "Featured" ? (
                            <><Sparkles className="w-3 h-3 mr-1" /> {service.badge.text}</>
                          ) : (
                            service.badge.text
                          )}
                        </Badge>
                      </div>
                    )}
                  </div>

                  <div className="p-3">
                    <h3 className="text-base font-light text-[#5d3f39] mb-1 font-playfair">{service.name}</h3>
                    <p className="text-xs text-[#5d3f39]/80 mb-2 line-clamp-2 font-light font-playfair">{service.description}</p>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-3 h-3 text-[#8b5d53]" />
                        <span className="text-xs text-[#5d3f39]/70">{service.duration}</span>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-[#8b5d53] hover:text-[#5d3f39] hover:bg-[#8b5d53]/10 p-2 h-auto text-xs"
                        onClick={() => handleServiceClick(
                          service.id === "facial-1" ? "facial-details" :
                          service.id === "massage-1" ? "massage-details" :
                          service.id === "facial-2" ? "collagen-therapy-details" :
                          service.id === "massage-2" ? "waxing-details" :
                          service.id === "facial-3" ? "lacarte-details" :
                          service.id === "package-1" ? "package-details" :
                          service.id
                        )}
                      >
                        View Details <ChevronRight className="w-3 h-3 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Main Services Sections - Only visible on desktop */}
        {/* Facial Details Section */}
        <section id="facial-details" className="py-16 border-t border-[#8b5d53]/10 hidden md:block">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <Badge className="mb-2 bg-[#8b5d53]/10 text-[#8b5d53] hover:bg-[#8b5d53]/20">Featured Treatment</Badge>
              <h2 className="text-3xl md:text-4xl font-light tracking-wide mb-4 text-[#5d3f39] uppercase font-serif">Facial Treatments</h2>
              <p className="text-lg text-[#5d3f39]/80 max-w-2xl mx-auto font-light">
                Every face is different. That&apos;s why we tailor treatments to what your skin actually needs.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-6"></div>
            </div>

            <ServiceDetails service={facialDetails} />
          </div>
        </section>

        {/* Lash Services Section */}
        <section id="massage-details" className="py-16 bg-[#8b5d53]/5 border-t border-[#8b5d53]/10 hidden md:block">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <Badge className="mb-2 bg-[#8b5d53]/10 text-[#8b5d53] hover:bg-[#8b5d53]/20">Featured Treatment</Badge>
              <h2 className="text-3xl md:text-4xl font-light tracking-wide mb-4 text-[#5d3f39] uppercase font-serif">Lash Services</h2>
              <p className="text-lg text-[#5d3f39]/80 max-w-2xl mx-auto font-light">
                Our lash enhancement services help you achieve beautiful, natural-looking results that frame your eyes perfectly.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-6"></div>
            </div>

            <ServiceDetails service={massageDetails} />
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="relative py-16">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-light tracking-wide mb-4 text-[#5d3f39] uppercase font-serif">What Our Clients Say</h2>
              <p className="text-lg text-[#5d3f39]/80 max-w-2xl mx-auto font-light">
                Real reviews from real clients who&apos;ve experienced the glow transformation.
              </p>
              <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-6"></div>
            </div>

            {/* Stacked Reviews Layout */}
            <div className="space-y-8">
              {/* Yelp Reviews */}
              <div className="bg-white/90 rounded-lg p-6 shadow-sm border border-[#8b5d53]/10">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">Y</span>
                  </div>
                  <h3 className="text-xl font-light text-[#5d3f39] font-playfair">Yelp Reviews</h3>
                </div>
                {/* Yelp Reviews Widget */}
                <div className="min-h-[300px]">
                  {!scriptsLoaded && (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-pulse text-[#5d3f39]/60">Loading reviews...</div>
                    </div>
                  )}
                  <div className="elfsight-app-8a2a1bb6-4be5-4bae-9408-f3a1690fc11a" data-elfsight-app-lazy></div>
                </div>
              </div>

              {/* Google Reviews */}
              <div className="bg-white/90 rounded-lg p-6 shadow-sm border border-[#8b5d53]/10" id="google-reviews">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-[#4285F4] rounded flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-sm">G</span>
                  </div>
                  <h3 className="text-xl font-light text-[#5d3f39] font-playfair">Google Reviews</h3>
                </div>
                {/* Google Reviews Widget */}
                <div className="min-h-[300px] google-reviews-container">
                  {!scriptsLoaded && (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-pulse text-[#5d3f39]/60">Loading reviews...</div>
                    </div>
                  )}
                  {/* Trustindex widget container with unique ID */}
                  <div
                    id="google-reviews-widget"
                    className="ti-widget"
                    data-layout-id="a0845c0467c297567c962ae15e4"
                    data-widget-type="slider"
                    data-platform="google"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Service Dialogs - Used for both desktop and mobile popups */}
        <ServiceDialog
          service={packageDetails}
          open={openServiceId === 'package-details'}
          onOpenChange={(open) => !open && setOpenServiceId(null)}
        />
        <ServiceDialog
          service={waxingDetails}
          open={openServiceId === 'waxing-details'}
          onOpenChange={(open) => !open && setOpenServiceId(null)}
        />
        <ServiceDialog
          service={laCarteDetails}
          open={openServiceId === 'lacarte-details'}
          onOpenChange={(open) => !open && setOpenServiceId(null)}
        />
        <ServiceDialog
          service={collagenTherapyDetails}
          open={openServiceId === 'collagen-therapy-details'}
          onOpenChange={(open) => !open && setOpenServiceId(null)}
        />
        <ServiceDialog
          service={facialDetails}
          open={openServiceId === 'facial-details'}
          onOpenChange={(open) => !open && setOpenServiceId(null)}
        />
        <ServiceDialog
          service={massageDetails}
          open={openServiceId === 'massage-details'}
          onOpenChange={(open) => !open && setOpenServiceId(null)}
        />

        {/* CTA Section */}
        <section className="py-16 px-4 container mx-auto text-center">
          <div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-light tracking-wide mb-4 text-[#8b5d53] uppercase font-serif">READY TO TREAT YOURSELF?</h2>
            <p className="text-lg text-[#5d3f39]/70 max-w-2xl mx-auto mb-8 font-extralight font-serif">
              Book your appointment today and experience the ultimate in relaxation and rejuvenation.
            </p>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mb-8"></div>
            <Button size="lg" className="px-8 bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white shadow-md font-serif font-extralight" asChild>
              <Link href="/booking">Book Now <ChevronRight className="w-4 h-4 ml-1" /></Link>
            </Button>
          </div>
        </section>
      </section>

      <SiteFooter />
    </div>
  );
}