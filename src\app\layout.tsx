import type { <PERSON><PERSON><PERSON>, Viewport } from "next";
import { Playfair_Display, Noto_Serif, Inter } from "next/font/google";
import { Suspense } from "react";
import { NavWrapper } from "@/components/ui/nav-wrapper";
import { <PERSON>riptLoader } from "@/components/ui/script-loader";
import "./globals.css";

// Use Inter as a replacement for Geist
const geistSans = Inter({
  subsets: ["latin"],
  variable: "--font-geist-sans",
  display: "swap",
});

// Use Inter for monospace as well since it's a good substitute
const geistMono = Inter({
  subsets: ["latin"],
  variable: "--font-geist-mono",
  display: "swap",
});

// Add Playfair Display as a serif font (similar to Main Serif)
const playfairDisplay = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-main-serif',
  display: 'swap',
});

// Add Noto Serif font
const notoSerif = Noto_Serif({
  subsets: ['latin'],
  variable: '--font-noto-serif',
  display: 'swap',
});

// Keep the local font configuration as a backup
// Uncomment and use this if you have the actual Main Serif font files
/*
const mainSerif = localFont({
  src: [
    {
      path: '../../public/fonts/MainSerif-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/MainSerif-Medium.woff2',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/MainSerif-Bold.woff2',
      weight: '700',
      style: 'normal',
    }
  ],
  variable: '--font-main-serif',
  display: 'swap',
});
*/

export const metadata: Metadata = {
  title: "GlowByBry - Beauty Spa Services",
  description: "Experience transformative beauty treatments that enhance your natural radiance at GlowByBry",
  keywords: ["beauty", "spa", "facial", "massage", "skincare", "treatments"],
  authors: [{ name: "SolanaSergio" }],
  creator: "SolanaSergio",
  formatDetection: {
    telephone: true,
    email: true,
    address: true,
  },
};

export const viewport: Viewport = {
  themeColor: '#f0e4df',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://images.unsplash.com" />
        <link rel="preconnect" href="https://images.pexels.com" />
        <link rel="dns-prefetch" href="https://images.unsplash.com" />
        <link rel="dns-prefetch" href="https://images.pexels.com" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${playfairDisplay.variable} ${notoSerif.variable} antialiased`}
        suppressHydrationWarning
      >
        <NavWrapper />
        <Suspense>
          {children}
        </Suspense>
        <ScriptLoader />
      </body>
    </html>
  );
}