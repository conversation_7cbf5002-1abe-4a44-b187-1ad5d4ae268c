"use client";

import Script from "next/script";
import { useEffect } from "react";

// Declare global window interfaces for both widgets
declare global {
  interface Window {
    Trustindex?: {
      init: () => void;
      scan?: () => void;
      resize_widgets?: () => void;
    };
    eapps?: {
      init?: () => void;
      initializeWidgets?: () => void;
      [key: string]: any;
    };
  }
}

export function ScriptLoader() {
  useEffect(() => {
    // Monitor for widget placement and ensure they stay in correct containers
    const observeWidgetPlacement = () => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // Log all added elements for debugging
              if (element.matches('[data-layout-id], [class*="trustindex"], iframe[src*="trustindex"], [id*="trustindex"]')) {
                console.log('🔍 Widget element detected:', {
                  tagName: element.tagName,
                  className: element.className,
                  id: element.id,
                  dataLayoutId: element.getAttribute('data-layout-id'),
                  parentElement: element.parentElement?.tagName,
                  parentId: element.parentElement?.id,
                  parentClass: element.parentElement?.className
                });
              }

              // Check for Trustindex widgets that might be misplaced
              if (element.matches('[data-layout-id], [class*="trustindex"], iframe[src*="trustindex"], [id*="trustindex"]') ||
                  element.querySelector('[data-layout-id], [class*="trustindex"], iframe[src*="trustindex"], [id*="trustindex"]')) {

                const targetContainer = document.querySelector('#google-reviews-widget');

                // Check if this is a widget that should be in our target container
                const isOurWidget = element.getAttribute('data-layout-id') === 'a0845c0467c297567c962ae15e4' ||
                                   element.querySelector('[data-layout-id="a0845c0467c297567c962ae15e4"]') ||
                                   element.className.includes('ti-widget') ||
                                   element.querySelector('.ti-widget');

                // If it's our widget and it's not in the target container, move it
                if (isOurWidget && targetContainer && !targetContainer.contains(element) && element !== targetContainer) {
                  console.log('🔄 Trustindex widget detected outside target container, moving it...');
                  console.log('Element details:', {
                    element: element,
                    currentParent: element.parentElement,
                    targetContainer: targetContainer,
                    elementPosition: element.getBoundingClientRect(),
                    isDirectBodyChild: element.parentElement === document.body
                  });

                  try {
                    // If it's a direct child of body, remove it and don't move it
                    if (element.parentElement === document.body) {
                      console.log('🗑️ Removing widget from body (duplicate widget)');
                      element.remove();
                    } else {
                      // Move it to the correct container
                      targetContainer.appendChild(element);
                      console.log('✅ Trustindex widget moved to correct container');
                    }

                    // Log final position
                    setTimeout(() => {
                      if (!element.isConnected) {
                        console.log('Widget was removed from DOM');
                      } else {
                        console.log('Final widget position:', element.getBoundingClientRect());
                      }
                    }, 100);
                  } catch (error) {
                    console.error('❌ Error handling Trustindex widget:', error);
                  }
                }
              }
            }
          });
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return observer;
    };

    const observer = observeWidgetPlacement();

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <>
      {/* Elfsight script for Yelp reviews */}
      <Script
        src="https://static.elfsight.com/platform/platform.js"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('✅ Elfsight script loaded successfully');

          // Initialize Elfsight widgets
          setTimeout(() => {
            if (typeof window !== 'undefined' && window.eapps) {
              console.log('🔄 Initializing Elfsight widgets...');
              console.log('Available eapps methods:', Object.keys(window.eapps));
              try {
                // Check if init method exists, otherwise try other methods
                if (typeof window.eapps.init === 'function') {
                  window.eapps.init();
                } else if (typeof window.eapps.initializeWidgets === 'function') {
                  window.eapps.initializeWidgets();
                } else {
                  console.log('No init method found, widgets should auto-initialize');
                }
                console.log('✅ Elfsight widgets initialized');
              } catch (error) {
                console.error('❌ Error initializing Elfsight:', error);
              }
            } else {
              console.log('⚠️ Elfsight not available yet, widgets should auto-initialize');
            }
          }, 500);
        }}
        onError={(error) => {
          console.error('❌ Failed to load Elfsight script:', error);
        }}
      />

      {/* Trustindex script for Google reviews */}
      <Script
        src="https://cdn.trustindex.io/loader.js?a0845c0467c297567c962ae15e4"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('✅ Trustindex script loaded successfully');

          // Wait for DOM to be ready and then configure the target container
          setTimeout(() => {
            const targetContainer = document.querySelector('#google-reviews-widget');
            if (targetContainer) {
              console.log('🔄 Configuring Trustindex target container...');
              console.log('Target container details:', {
                element: targetContainer,
                position: targetContainer.getBoundingClientRect(),
                parent: targetContainer.parentElement,
                parentPosition: targetContainer.parentElement?.getBoundingClientRect()
              });

              // Clear any existing content first to prevent duplicates
              targetContainer.innerHTML = '';

              // Ensure the container has the correct attributes
              targetContainer.setAttribute('data-layout-id', 'a0845c0467c297567c962ae15e4');
              targetContainer.setAttribute('data-widget-type', 'slider');
              targetContainer.setAttribute('data-platform', 'google');

              console.log('✅ Trustindex target container configured');

              // Initialize Trustindex if available
              if (typeof window !== 'undefined' && window.Trustindex) {
                console.log('🔄 Initializing Trustindex...');
                try {
                  // Remove any existing widgets from body before initializing
                  const existingBodyWidgets = document.querySelectorAll('body > .ti-widget, body > [data-layout-id], body > [class*="trustindex"]');
                  existingBodyWidgets.forEach(widget => {
                    console.log('🗑️ Removing existing body widget before init:', widget);
                    widget.remove();
                  });

                  window.Trustindex.init();
                  console.log('✅ Trustindex initialized successfully');

                  // Check for widgets after initialization and clean up any duplicates
                  setTimeout(() => {
                    const allTrustindexElements = document.querySelectorAll('[data-layout-id*="a0845c0467c297567c962ae15e4"], [class*="trustindex"], iframe[src*="trustindex"]');
                    console.log('🔍 All Trustindex elements found:', allTrustindexElements);

                    allTrustindexElements.forEach((el, index) => {
                      const isInTargetContainer = targetContainer.contains(el);
                      const isDirectBodyChild = el.parentElement === document.body;

                      console.log(`Element ${index}:`, {
                        element: el,
                        position: el.getBoundingClientRect(),
                        parent: el.parentElement,
                        isInTargetContainer: isInTargetContainer,
                        isDirectBodyChild: isDirectBodyChild
                      });

                      // Remove any widgets that are direct children of body
                      if (isDirectBodyChild) {
                        console.log('🗑️ Removing duplicate widget from body:', el);
                        el.remove();
                      }
                    });
                  }, 2000);
                } catch (error) {
                  console.error('❌ Error initializing Trustindex:', error);
                }
              } else {
                console.log('⚠️ Trustindex object not available, waiting for auto-initialization...');
              }
            } else {
              console.warn('⚠️ Trustindex target container not found');
              // Log all possible containers for debugging
              const allContainers = document.querySelectorAll('[id*="google"], [class*="google"], [data-widget-id]');
              console.log('Available containers:', allContainers);
            }
          }, 1000);
        }}
        onError={(error) => {
          console.error('❌ Failed to load Trustindex script:', error);
        }}
      />
    </>
  );
}