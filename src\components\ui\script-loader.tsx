"use client";

import <PERSON>ript from "next/script";
import { useEffect } from "react";

// Declare global window interface for Trustindex
declare global {
  interface Window {
    Trustindex?: {
      init: () => void;
      scan?: () => void;
      resize_widgets?: () => void;
    };
  }
}

export function ScriptLoader() {
  useEffect(() => {
    // Monitor for Trustindex widgets and ensure they're in the correct containers
    const observeWidgetPlacement = () => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // Check if this is a Trustindex widget that might be misplaced
              if (element.matches('[data-layout-id*="a0845c0467c297567c962ae15e4"], [class*="trustindex"], iframe[src*="trustindex"]') ||
                  element.querySelector('[data-layout-id*="a0845c0467c297567c962ae15e4"], [class*="trustindex"], iframe[src*="trustindex"]')) {
                
                const targetContainer = document.querySelector('#google-reviews-widget');
                
                // If the widget was added outside its target container, move it
                if (targetContainer && !targetContainer.contains(element) && element !== targetContainer) {
                  console.log('Trustindex widget detected outside target container, moving it...');
                  
                  // Move the widget to the correct container
                  try {
                    targetContainer.appendChild(element);
                    console.log('✅ Widget moved to correct container');
                  } catch (error) {
                    console.error('Error moving widget:', error);
                  }
                }
              }
            }
          });
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return observer;
    };

    const observer = observeWidgetPlacement();

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <>
      {/* Trustindex script using Next.js Script component */}
      <Script 
        src="https://cdn.trustindex.io/loader.js?a0845c0467c297567c962ae15e4"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('✅ Trustindex script loaded successfully');
          
          // Set up proper container targeting
          setTimeout(() => {
            const targetContainer = document.querySelector('#google-reviews-widget');
            if (targetContainer) {
              // Ensure the container has the right attributes
              targetContainer.setAttribute('data-layout-id', 'a0845c0467c297567c962ae15e4');
              targetContainer.setAttribute('data-platform', 'google');
              console.log('Target container configured for Trustindex');
            }
          }, 100);

          // Initialize Trustindex if available
          if (typeof window !== 'undefined' && window.Trustindex) {
            console.log('Initializing Trustindex...');
            try {
              window.Trustindex.init();
            } catch (error) {
              console.error('Error initializing Trustindex:', error);
            }
          } else {
            console.log('Trustindex not available yet, waiting for auto-initialization...');
          }
        }}
        onError={(error) => {
          console.error('❌ Failed to load Trustindex script:', error);
        }}
      />
    </>
  );
} 