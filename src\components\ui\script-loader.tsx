"use client";

import <PERSON>ript from "next/script";
import { useEffect } from "react";

// Declare global window interfaces for both widgets
declare global {
  interface Window {
    Trustindex?: {
      init: () => void;
      scan?: () => void;
      resize_widgets?: () => void;
    };
    eapps?: {
      init?: () => void;
      initializeWidgets?: () => void;
      [key: string]: any;
    };
  }
}

export function ScriptLoader() {
  useEffect(() => {
    // Monitor for widget placement and ensure they stay in correct containers
    const observeWidgetPlacement = () => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // Only handle widgets that are direct children of body (misplaced)
              if (element.parentElement === document.body &&
                  element.matches('.ti-widget, [data-layout-id], [class*="trustindex"]')) {

                console.log('🔍 Misplaced widget detected in body:', {
                  tagName: element.tagName,
                  className: element.className,
                  id: element.id,
                  dataLayoutId: element.getAttribute('data-layout-id')
                });

                const targetContainer = document.querySelector('#google-reviews-widget');

                if (targetContainer) {
                  console.log('🔄 Moving misplaced widget to target container');
                  try {
                    targetContainer.appendChild(element);
                    console.log('✅ Widget moved successfully');
                  } catch (error) {
                    console.error('❌ Error moving widget:', error);
                    // If moving fails, hide it
                    (element as HTMLElement).style.display = 'none';
                  }
                } else {
                  console.log('🗑️ No target container found, hiding widget');
                  (element as HTMLElement).style.display = 'none';
                }
              }
            }
          });
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return observer;
    };

    const observer = observeWidgetPlacement();

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <>
      {/* Elfsight script for Yelp reviews */}
      <Script
        src="https://static.elfsight.com/platform/platform.js"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('✅ Elfsight script loaded successfully');

          // Initialize Elfsight widgets
          setTimeout(() => {
            if (typeof window !== 'undefined' && window.eapps) {
              console.log('🔄 Initializing Elfsight widgets...');
              console.log('Available eapps methods:', Object.keys(window.eapps));
              try {
                // Check if init method exists, otherwise try other methods
                if (typeof window.eapps.init === 'function') {
                  window.eapps.init();
                } else if (typeof window.eapps.initializeWidgets === 'function') {
                  window.eapps.initializeWidgets();
                } else {
                  console.log('No init method found, widgets should auto-initialize');
                }
                console.log('✅ Elfsight widgets initialized');
              } catch (error) {
                console.error('❌ Error initializing Elfsight:', error);
              }
            } else {
              console.log('⚠️ Elfsight not available yet, widgets should auto-initialize');
            }
          }, 500);
        }}
        onError={(error) => {
          console.error('❌ Failed to load Elfsight script:', error);
        }}
      />

      {/* Trustindex script for Google reviews */}
      <Script
        src="https://cdn.trustindex.io/loader.js?a0845c0467c297567c962ae15e4"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('✅ Trustindex script loaded successfully');

          // Simple approach - let Trustindex auto-initialize and then move widgets if needed
          setTimeout(() => {
            console.log('🔍 Checking for Trustindex widgets...');

            // Find all Trustindex widgets
            const allWidgets = document.querySelectorAll('.ti-widget, [data-layout-id], [class*="trustindex"]');
            console.log('Found widgets:', allWidgets);

            const targetContainer = document.querySelector('#google-reviews-widget');
            console.log('Target container:', targetContainer);

            if (targetContainer && allWidgets.length > 0) {
              allWidgets.forEach((widget, index) => {
                const isInTarget = targetContainer.contains(widget);
                const isBodyChild = widget.parentElement === document.body;

                console.log(`Widget ${index}:`, {
                  element: widget,
                  isInTarget,
                  isBodyChild,
                  parent: widget.parentElement?.tagName
                });

                // If widget is not in target and is a body child, move it
                if (!isInTarget && isBodyChild) {
                  console.log('🔄 Moving widget to target container');
                  targetContainer.appendChild(widget);
                }
              });
            }

            // Initialize Trustindex if available
            if (typeof window !== 'undefined' && window.Trustindex) {
              console.log('🔄 Initializing Trustindex...');
              try {
                window.Trustindex.init();
                console.log('✅ Trustindex initialized');
              } catch (error) {
                console.error('❌ Error initializing Trustindex:', error);
              }
            }
          }, 2000);
        }}
        onError={(error) => {
          console.error('❌ Failed to load Trustindex script:', error);
        }}
      />
    </>
  );
}